# MentorMatch Database

## Structure

```
database/
├── schema.sql        # Main database schema
├── seeds/           # Seed data for development/testing
│   └── initial_data.sql
└── migrations/      # Database migrations
```

## Setup

1. Create database:
```sql
CREATE DATABASE mentormatch;
```

2. Apply schema:
```bash
psql -U postgres -d mentormatch -f schema.sql
```

3. Load test data:
```bash
psql -U postgres -d mentormatch -f seeds/initial_data.sql
```

## Backup

```bash
pg_dump -U postgres mentormatch > backup.sql
```
