version: '3.8'

services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
    environment:
      - NODE_ENV=development

  backend:
    build: ./backend
    ports:
      - "4000:4000"
    volumes:
      - ./backend:/app
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/mentormatch

  ai-engine:
    build: ./ai-engine
    ports:
      - "5000:5000"
    volumes:
      - ./ai-engine:/app

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=mentormatch
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
