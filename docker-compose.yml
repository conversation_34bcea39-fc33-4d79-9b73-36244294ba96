version: '3.8'

services:
  # Frontend Service (React.js)
  frontend:
    build:
      context: ./frontend
      target: development
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://localhost:4000
      - REACT_APP_AI_ENGINE_URL=http://localhost:5000
    depends_on:
      - backend
    networks:
      - mentormatch-network
    restart: unless-stopped

  # Backend Service (Node.js/Express)
  backend:
    build:
      context: ./backend
    ports:
      - "4000:4000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - PORT=4000
      - DATABASE_URL=**************************************/mentormatch
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret-key
      - AI_ENGINE_URL=http://ai-engine:5000
    depends_on:
      - db
      - redis
    networks:
      - mentormatch-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # AI Engine Service (Python/FastAPI)
  ai-engine:
    build:
      context: ./ai-engine
    ports:
      - "5000:5000"
    volumes:
      - ./ai-engine:/app
      - ai_models:/app/models
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=**************************************/mentormatch
      - REDIS_URL=redis://redis:6379
      - MODEL_PATH=/app/models
    depends_on:
      - db
      - redis
    networks:
      - mentormatch-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "healthcheck.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=mentormatch
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./database/seeds/initial_data.sql:/docker-entrypoint-initdb.d/02-seeds.sql
    ports:
      - "5432:5432"
    networks:
      - mentormatch-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d mentormatch"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mentormatch-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Nginx Reverse Proxy (for production)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - mentormatch-network
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  ai_models:
    driver: local

networks:
  mentormatch-network:
    driver: bridge
