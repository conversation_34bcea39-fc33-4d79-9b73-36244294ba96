# Start production environment
Write-Host "Starting MentorMatch production environment..." -ForegroundColor Green

# Check if Docker is running
if (-not (Get-Process "Docker Desktop" -ErrorAction SilentlyContinue)) {
    Write-Host "Docker Desktop is not running. Please start Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "Error: .env file not found. Please create it from .env.example" -ForegroundColor Red
    exit 1
}

# Build and start services
Write-Host "Building and starting production services..." -ForegroundColor Blue
docker-compose -f docker-compose.prod.yml up --build -d

# Show status
Write-Host "Checking service status..." -ForegroundColor Blue
docker-compose -f docker-compose.prod.yml ps

Write-Host "Production environment started successfully!" -ForegroundColor Green
Write-Host "Application: http://localhost" -ForegroundColor Cyan
