{"name": "mentormatch-frontend", "version": "1.0.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.9.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.1", "react-router-dom": "^6.14.1", "react-scripts": "5.0.1", "axios": "^1.4.0", "socket.io-client": "^4.7.1", "web-vitals": "^3.3.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://backend:4000"}