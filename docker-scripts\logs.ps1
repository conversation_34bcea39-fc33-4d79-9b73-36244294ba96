# View logs for all services or specific service
param(
    [string]$Service = ""
)

Write-Host "Viewing MentorMatch logs..." -ForegroundColor Green

if ($Service -eq "") {
    Write-Host "Showing logs for all services..." -ForegroundColor Blue
    docker-compose logs -f
} else {
    Write-Host "Showing logs for service: $Service" -ForegroundColor Blue
    docker-compose logs -f $Service
}

# Usage examples:
# .\logs.ps1                 # View all logs
# .\logs.ps1 frontend        # View frontend logs only
# .\logs.ps1 backend         # View backend logs only
# .\logs.ps1 ai-engine       # View AI engine logs only
