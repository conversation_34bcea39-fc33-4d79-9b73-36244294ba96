-- =================================================================
-- سكريبت إنشاء قاعدة بيانات MentorMatch
-- PostgreSQL
-- =================================================================

-- تفعيل امتداد لتوليد UUIDs إذا لم يكن مفعلًا
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =================================================================
-- جدول المستخدمين (Users)
-- يخزن معلومات تسجيل الدخول الأساسية ودور المستخدم
-- =================================================================
CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), -- المعرف الفريد للمستخدم
    email VARCHAR(255) UNIQUE NOT NULL, -- البريد الإلكتروني، يجب أن يكون فريدًا
    password_hash VARCHAR(255) NOT NULL, -- تجزئة كلمة المرور
    user_role VARCHAR(10) NOT NULL CHECK (user_role IN ('mentee', 'mentor')), -- دور المستخدم: 'mentee' أو 'mentor'
    is_active BOOLEAN DEFAULT TRUE, -- حالة الحساب (نشط/غير نشط)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- تاريخ ووقت إنشاء الحساب
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP -- تاريخ ووقت آخر تحديث
);

-- =================================================================
-- جدول الملفات الشخصية للمستخدمين (User Profiles)
-- يخزن معلومات تفصيلية عن المستخدمين (سواء كانوا Mentees أو Mentors)
-- =================================================================
CREATE TABLE user_profiles (
    profile_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), -- المعرف الفريد للملف الشخصي
    user_id UUID UNIQUE NOT NULL REFERENCES users(user_id) ON DELETE CASCADE, -- ربط مع جدول المستخدمين
    full_name VARCHAR(255) NOT NULL, -- الاسم الكامل
    bio TEXT, -- نبذة شخصية
    avatar_url VARCHAR(255), -- رابط صورة الملف الشخصي
    linkedin_profile VARCHAR(255), -- رابط ملف لينكدإن (اختياري)
    website VARCHAR(255), -- رابط الموقع الشخصي (اختياري)
    -- حقول خاصة بالمرشدين (Mentors)
    mentor_headline VARCHAR(255), -- عنوان رئيسي للمرشد (مثل "خبير تسويق رقمي")
    years_of_experience INT, -- سنوات الخبرة
    -- حقول خاصة بالباحثين عن الإرشاد (Mentees)
    mentee_learning_goals TEXT, -- أهداف التعلم للباحث عن الإرشاد
    -- حقول مشتركة
    skills TEXT[], -- مصفوفة من المهارات (مثال: {'JavaScript', 'Project Management'})
    interests TEXT[], -- مصفوفة من الاهتمامات
    availability JSONB, -- معلومات التوفر (مثال: {'monday': ['09:00-12:00', '14:00-17:00']})
    communication_preferences JSONB, -- تفضيلات التواصل (مثال: {'preferred_platform': 'app_video', 'response_time': '24h'})
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP -- تاريخ ووقت آخر تحديث
);

-- =================================================================
-- جدول مجالات خبرة المرشدين (Mentor Expertise Areas)
-- يحدد مجالات الخبرة التي يمكن للمرشد تقديم الإرشاد فيها
-- =================================================================
CREATE TABLE mentor_expertise_areas (
    expertise_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), -- المعرف الفريد لمجال الخبرة
    mentor_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE, -- ربط مع جدول المستخدمين (المرشد)
    area_name VARCHAR(255) NOT NULL, -- اسم مجال الخبرة (مثال: "تطوير الويب", "ريادة الأعمال")
    description TEXT, -- وصف موجز للخبرة في هذا المجال
    CONSTRAINT fk_mentor_expertise_user CHECK ( (SELECT user_role FROM users WHERE user_id = mentor_user_id) = 'mentor' ) -- التأكد أن المستخدم هو مرشد
);

-- =================================================================
-- جدول أهداف الباحثين عن الإرشاد (Mentee Goals)
-- يحدد الأهداف التي يسعى الباحث عن الإرشاد لتحقيقها
-- =================================================================
CREATE TABLE mentee_goals (
    goal_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), -- المعرف الفريد للهدف
    mentee_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE, -- ربط مع جدول المستخدمين (الباحث عن الإرشاد)
    goal_description TEXT NOT NULL, -- وصف الهدف
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'achieved', 'on_hold', 'abandoned')), -- حالة الهدف
    target_date DATE, -- تاريخ مستهدف لتحقيق الهدف (اختياري)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- تاريخ إنشاء الهدف
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- تاريخ آخر تحديث للهدف
    CONSTRAINT fk_mentee_goal_user CHECK ( (SELECT user_role FROM users WHERE user_id = mentee_user_id) = 'mentee' ) -- التأكد أن المستخدم هو باحث عن الإرشاد
);

-- =================================================================
-- جدول جلسات الإرشاد (Mentorship Sessions)
-- يخزن معلومات عن الجلسات المجدولة والمكتملة
-- =================================================================
CREATE TABLE mentorship_sessions (
    session_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), -- المعرف الفريد للجلسة
    mentee_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE SET NULL, -- ربط مع الباحث عن الإرشاد
    mentor_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE SET NULL, -- ربط مع المرشد
    scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL, -- وقت الجلسة المجدول
    duration_minutes INT NOT NULL DEFAULT 60, -- مدة الجلسة بالدقائق
    session_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (session_status IN ('pending_mentee_confirm', 'pending_mentor_confirm', 'confirmed', 'completed', 'cancelled_by_mentee', 'cancelled_by_mentor', 'rescheduled')), -- حالة الجلسة
    communication_tool VARCHAR(50), -- أداة التواصل المستخدمة (مثل "app_video", "zoom_link")
    tool_link VARCHAR(255), -- رابط الأداة إذا كان خارجيًا
    session_topic TEXT, -- موضوع الجلسة الرئيسي
    mentee_preparation TEXT, -- ما يجب على الباحث عن الإرشاد تحضيره
    mentor_preparation TEXT, -- ما يجب على المرشد تحضيره
    ai_generated_notes TEXT, -- ملاحظات تم إنشاؤها بواسطة الذكاء الاصطناعي (إن وجدت)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_session_mentee CHECK ( (SELECT user_role FROM users WHERE user_id = mentee_user_id) = 'mentee' ),
    CONSTRAINT fk_session_mentor CHECK ( (SELECT user_role FROM users WHERE user_id = mentor_user_id) = 'mentor' )
);

-- =================================================================
-- جدول التقييمات والملاحظات (Feedback)
-- يخزن التقييمات والملاحظات المتبادلة بعد الجلسات
-- =================================================================
CREATE TABLE feedback (
    feedback_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), -- المعرف الفريد للتقييم
    session_id UUID NOT NULL REFERENCES mentorship_sessions(session_id) ON DELETE CASCADE, -- ربط مع الجلسة
    giver_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE, -- المستخدم الذي قدم التقييم
    receiver_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE, -- المستخدم الذي تلقى التقييم
    rating INT CHECK (rating >= 1 AND rating <= 5), -- التقييم (من 1 إلى 5 نجوم)
    comment TEXT, -- تعليق إضافي
    feedback_type VARCHAR(20) NOT NULL CHECK (feedback_type IN ('mentee_to_mentor', 'mentor_to_mentee')), -- نوع التقييم
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =================================================================
-- جدول اقتراحات المطابقة بواسطة الذكاء الاصطناعي (AI Match Suggestions)
-- يخزن الاقتراحات التي يقدمها نظام الذكاء الاصطناعي
-- =================================================================
CREATE TABLE ai_match_suggestions (
    suggestion_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), -- المعرف الفريد للاقتراح
    mentee_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE, -- الباحث عن الإرشاد الذي تم اقتراح مرشد له
    suggested_mentor_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE, -- المرشد المقترح
    match_score FLOAT, -- درجة المطابقة (مثلاً، بين 0 و 1)
    reasoning TEXT, -- سبب الاقتراح أو العوامل التي تم أخذها في الاعتبار
    suggestion_status VARCHAR(20) DEFAULT 'pending' CHECK (suggestion_status IN ('pending', 'accepted', 'rejected', 'ignored')), -- حالة الاقتراح من قبل الباحث
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_suggestion_mentee CHECK ( (SELECT user_role FROM users WHERE user_id = mentee_user_id) = 'mentee' ),
    CONSTRAINT fk_suggestion_mentor CHECK ( (SELECT user_role FROM users WHERE user_id = suggested_mentor_user_id) = 'mentor' )
);

-- =================================================================
-- جدول الإشعارات (Notifications)
-- لتخزين الإشعارات المرسلة للمستخدمين
-- =================================================================
CREATE TABLE notifications (
    notification_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE, -- المستخدم الذي سيتلقى الإشعار
    notification_type VARCHAR(50) NOT NULL, -- نوع الإشعار (مثال: 'new_session_request', 'session_confirmed', 'feedback_received')
    message TEXT NOT NULL, -- نص الإشعار
    related_entity_type VARCHAR(50), -- نوع الكيان المرتبط (مثال: 'session', 'user')
    related_entity_id UUID, -- معرّف الكيان المرتبط
    is_read BOOLEAN DEFAULT FALSE, -- هل تمت قراءة الإشعار
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =================================================================
-- إنشاء فهارس (Indexes) لتحسين أداء الاستعلامات
-- =================================================================
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_mentor_expertise_mentor_id ON mentor_expertise_areas(mentor_user_id);
CREATE INDEX idx_mentee_goals_mentee_id ON mentee_goals(mentee_user_id);
CREATE INDEX idx_sessions_mentee_id ON mentorship_sessions(mentee_user_id);
CREATE INDEX idx_sessions_mentor_id ON mentorship_sessions(mentor_user_id);
CREATE INDEX idx_sessions_scheduled_time ON mentorship_sessions(scheduled_time);
CREATE INDEX idx_feedback_session_id ON feedback(session_id);
CREATE INDEX idx_feedback_giver_id ON feedback(giver_user_id);
CREATE INDEX idx_feedback_receiver_id ON feedback(receiver_user_id);
CREATE INDEX idx_ai_suggestions_mentee_id ON ai_match_suggestions(mentee_user_id);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);

-- =================================================================
-- وظائف Trigger لتحديث حقل updated_at تلقائيًا
-- =================================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق الـ Trigger على الجداول التي تحتوي على updated_at
CREATE TRIGGER trigger_users_updated_at
BEFORE UPDATE ON users
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_user_profiles_updated_at
BEFORE UPDATE ON user_profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_mentee_goals_updated_at
BEFORE UPDATE ON mentee_goals
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_mentorship_sessions_updated_at
BEFORE UPDATE ON mentorship_sessions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- نهاية السكريبت
