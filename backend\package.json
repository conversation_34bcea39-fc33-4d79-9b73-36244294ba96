{"name": "mentormatch-backend", "version": "1.0.0", "description": "MentorMatch Backend API", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.1", "pg": "^8.11.1", "pg-hstore": "^2.3.4", "sequelize": "^6.32.1", "socket.io": "^4.7.1", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.3", "redis": "^4.6.7"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}