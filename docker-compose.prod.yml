version: '3.8'

services:
  # Frontend Service (Production build with Nginx)
  frontend:
    build:
      context: ./frontend
      target: production
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=${REACT_APP_API_URL:-http://localhost:4000}
      - REACT_APP_AI_ENGINE_URL=${REACT_APP_AI_ENGINE_URL:-http://localhost:5000}
    depends_on:
      - backend
    networks:
      - mentormatch-network
    restart: unless-stopped

  # Backend Service (Production)
  backend:
    build:
      context: ./backend
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=production
      - PORT=4000
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
      - AI_ENGINE_URL=http://ai-engine:5000
    depends_on:
      - db
      - redis
    networks:
      - mentormatch-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # AI Engine Service (Production)
  ai-engine:
    build:
      context: ./ai-engine
    ports:
      - "5000:5000"
    volumes:
      - ai_models:/app/models
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - MODEL_PATH=/app/models
    depends_on:
      - db
      - redis
    networks:
      - mentormatch-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "healthcheck.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # PostgreSQL Database (Production)
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - mentormatch-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache (Production)
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    networks:
      - mentormatch-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - mentormatch-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  ai_models:
    driver: local

networks:
  mentormatch-network:
    driver: bridge
