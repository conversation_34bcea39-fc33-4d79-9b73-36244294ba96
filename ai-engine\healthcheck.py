#!/usr/bin/env python3
import sys
import requests
import os

def health_check():
    try:
        port = os.getenv('PORT', '5000')
        response = requests.get(f'http://localhost:{port}/health', timeout=5)
        if response.status_code == 200:
            print("Health check passed")
            sys.exit(0)
        else:
            print(f"Health check failed with status code: {response.status_code}")
            sys.exit(1)
    except Exception as e:
        print(f"Health check failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    health_check()
