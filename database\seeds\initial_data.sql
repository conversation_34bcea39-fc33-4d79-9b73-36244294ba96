-- Initial test data for MentorMatch

-- Test Users
INSERT INTO users (email, password_hash, user_role) VALUES
('<EMAIL>', '$2a$10$test_hash', 'mentor'),
('<EMAIL>', '$2a$10$test_hash', 'mentor'),
('<EMAIL>', '$2a$10$test_hash', 'mentee'),
('<EMAIL>', '$2a$10$test_hash', 'mentee');

-- Test Profiles
INSERT INTO user_profiles (user_id, full_name, bio, skills, interests) 
SELECT 
    user_id,
    CASE 
        WHEN user_role = 'mentor' THEN 'Test Mentor ' || ROW_NUMBER() OVER (PARTITION BY user_role ORDER BY user_id)
        ELSE 'Test Mentee ' || ROW_NUMBER() OVER (PARTITION BY user_role ORDER BY user_id)
    END,
    'Test bio',
    ARRAY['JavaScript', 'Python'],
    ARRA<PERSON>['Web Development', 'AI']
FROM users;
