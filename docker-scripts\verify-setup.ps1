# Verify Docker setup for MentorMatch
Write-Host "Verifying Docker setup for MentorMatch..." -ForegroundColor Green

# Check if Docker is installed
try {
    $dockerVersion = docker --version
    Write-Host "✓ Docker is installed: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Docker Desktop from: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
    exit 1
}

# Check if Docker Compose is available
try {
    $composeVersion = docker-compose --version
    Write-Host "✓ Docker Compose is available: $composeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker Compose is not available" -ForegroundColor Red
    exit 1
}

# Check if Docker daemon is running
try {
    docker info | Out-Null
    Write-Host "✓ Docker daemon is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker daemon is not running. Please start Docker Desktop." -ForegroundColor Red
    exit 1
}

# Check required files
$requiredFiles = @(
    "docker-compose.yml",
    "docker-compose.prod.yml",
    ".dockerignore",
    ".env.example",
    "frontend/Dockerfile",
    "backend/Dockerfile",
    "ai-engine/Dockerfile"
)

Write-Host "`nChecking required files..." -ForegroundColor Blue
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file exists" -ForegroundColor Green
    } else {
        Write-Host "✗ $file is missing" -ForegroundColor Red
    }
}

# Check if .env file exists
if (Test-Path ".env") {
    Write-Host "✓ .env file exists" -ForegroundColor Green
} else {
    Write-Host "⚠ .env file not found. Creating from .env.example..." -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "✓ .env file created from .env.example" -ForegroundColor Green
        Write-Host "Please review and update the .env file with your configuration." -ForegroundColor Yellow
    } else {
        Write-Host "✗ .env.example file not found" -ForegroundColor Red
    }
}

# Validate docker-compose.yml
Write-Host "`nValidating Docker Compose configuration..." -ForegroundColor Blue
try {
    docker-compose config | Out-Null
    Write-Host "✓ docker-compose.yml is valid" -ForegroundColor Green
} catch {
    Write-Host "✗ docker-compose.yml has errors" -ForegroundColor Red
}

# Validate production docker-compose.yml
try {
    docker-compose -f docker-compose.prod.yml config | Out-Null
    Write-Host "✓ docker-compose.prod.yml is valid" -ForegroundColor Green
} catch {
    Write-Host "✗ docker-compose.prod.yml has errors" -ForegroundColor Red
}

Write-Host "`nDocker setup verification completed!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Review and update .env file" -ForegroundColor White
Write-Host "2. Run: .\docker-scripts\start-dev.ps1" -ForegroundColor White
Write-Host "3. Access the application at http://localhost:3000" -ForegroundColor White
