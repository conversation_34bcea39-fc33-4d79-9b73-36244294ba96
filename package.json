{"name": "mentor-match", "version": "1.0.0", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"start": "concurrently \"npm run start:frontend\" \"npm run start:backend\" \"npm run start:ai\"", "start:frontend": "npm run start --workspace=frontend", "start:backend": "npm run start --workspace=backend", "start:ai": "python ai-engine/src/main.py", "install:all": "npm install && npm run install:frontend && npm run install:backend", "install:frontend": "npm install --workspace=frontend", "install:backend": "npm install --workspace=backend", "test": "npm run test --workspaces"}, "devDependencies": {"concurrently": "^7.0.0"}}