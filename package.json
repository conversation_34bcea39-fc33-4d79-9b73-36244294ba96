{"name": "mentor-match", "version": "1.0.0", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"start": "concurrently \"npm run start:frontend\" \"npm run start:backend\" \"npm run start:ai\"", "start:frontend": "npm run start --workspace=frontend", "start:backend": "npm run start --workspace=backend", "start:ai": "python ai-engine/src/main.py", "install:all": "npm install && npm run install:frontend && npm run install:backend", "install:frontend": "npm install --workspace=frontend", "install:backend": "npm install --workspace=backend", "test": "npm run test --workspaces", "docker:dev": "docker-compose up --build -d", "docker:prod": "docker-compose -f docker-compose.prod.yml up --build -d", "docker:stop": "docker-compose down && docker-compose -f docker-compose.prod.yml down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down --volumes && docker system prune -f"}, "devDependencies": {"concurrently": "^7.0.0"}}