# Docker Environment Setup for MentorMatch

This document provides comprehensive instructions for setting up and managing the Docker environment for the MentorMatch project.

## Prerequisites

- Docker Desktop installed and running
- Docker Compose v3.8 or higher
- PowerShell (for Windows scripts)

## Project Structure

```
MentorMatch/
├── frontend/                 # React.js frontend
│   ├── Dockerfile
│   ├── nginx.conf
│   └── package.json
├── backend/                  # Node.js/Express backend
│   ├── Dockerfile
│   ├── healthcheck.js
│   └── package.json
├── ai-engine/               # Python AI engine
│   ├── Dockerfile
│   ├── requirements.txt
│   └── healthcheck.py
├── nginx/                   # Nginx configuration
│   ├── nginx.conf
│   └── conf.d/
├── docker-scripts/          # Management scripts
│   ├── start-dev.ps1
│   ├── start-prod.ps1
│   ├── stop.ps1
│   ├── logs.ps1
│   └── clean.ps1
├── docker-compose.yml       # Development configuration
├── docker-compose.prod.yml  # Production configuration
├── .dockerignore
└── .env.example
```

## Services

### Development Environment

- **Frontend**: React.js development server (Port 3000)
- **Backend**: Node.js/Express API server (Port 4000)
- **AI Engine**: Python/FastAPI service (Port 5000)
- **Database**: PostgreSQL 15 (Port 5432)
- **Cache**: Redis 7 (Port 6379)

### Production Environment

- **Frontend**: React.js production build with Nginx (Port 3000)
- **Backend**: Node.js/Express API server (Port 4000)
- **AI Engine**: Python/FastAPI service (Port 5000)
- **Database**: PostgreSQL 15 (Internal)
- **Cache**: Redis 7 (Internal)
- **Nginx**: Reverse proxy (Port 80/443)

## Quick Start

### 1. Environment Setup

```powershell
# Copy environment file
Copy-Item .env.example .env

# Edit .env file with your configuration
notepad .env
```

### 2. Development Environment

```powershell
# Start development environment
.\docker-scripts\start-dev.ps1

# Or manually
docker-compose up --build -d
```

### 3. Production Environment

```powershell
# Start production environment
.\docker-scripts\start-prod.ps1

# Or manually
docker-compose -f docker-compose.prod.yml up --build -d
```

## Management Commands

### Start Services

```powershell
# Development
.\docker-scripts\start-dev.ps1

# Production
.\docker-scripts\start-prod.ps1
```

### Stop Services

```powershell
# Stop all services
.\docker-scripts\stop.ps1

# Or manually
docker-compose down
```

### View Logs

```powershell
# All services
.\docker-scripts\logs.ps1

# Specific service
.\docker-scripts\logs.ps1 frontend
.\docker-scripts\logs.ps1 backend
.\docker-scripts\logs.ps1 ai-engine
```

### Clean Up

```powershell
# Clean up resources
.\docker-scripts\clean.ps1
```

## Manual Docker Commands

### Build and Start

```bash
# Development
docker-compose up --build -d

# Production
docker-compose -f docker-compose.prod.yml up --build -d
```

### Service Management

```bash
# View running services
docker-compose ps

# View logs
docker-compose logs -f [service-name]

# Restart a service
docker-compose restart [service-name]

# Stop services
docker-compose down

# Stop and remove volumes (WARNING: Data loss!)
docker-compose down --volumes
```

### Individual Service Commands

```bash
# Build specific service
docker-compose build frontend

# Start specific service
docker-compose up frontend

# Execute commands in running container
docker-compose exec backend npm install
docker-compose exec ai-engine pip install new-package
```

## Environment Variables

Key environment variables (see `.env.example` for complete list):

```env
# Application
NODE_ENV=development
PORT=4000

# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/mentormatch
POSTGRES_DB=mentormatch
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# Redis
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=your-super-secret-jwt-key

# Frontend
REACT_APP_API_URL=http://localhost:4000
REACT_APP_AI_ENGINE_URL=http://localhost:5000
```

## Health Checks

All services include health checks:

- **Backend**: HTTP check on `/health` endpoint
- **AI Engine**: Python health check script
- **Database**: PostgreSQL `pg_isready` check
- **Redis**: Redis `ping` command

## Volumes

- **postgres_data**: PostgreSQL data persistence
- **redis_data**: Redis data persistence
- **ai_models**: AI model storage

## Networks

All services communicate through the `mentormatch-network` bridge network.

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 3000, 4000, 5000, 5432, 6379 are available
2. **Docker not running**: Start Docker Desktop
3. **Permission issues**: Run PowerShell as Administrator
4. **Build failures**: Check Dockerfile syntax and dependencies

### Debug Commands

```bash
# Check service status
docker-compose ps

# View service logs
docker-compose logs [service-name]

# Access service shell
docker-compose exec [service-name] /bin/sh

# Check network connectivity
docker-compose exec backend ping db
docker-compose exec backend ping redis
```

### Reset Environment

```bash
# Complete reset (WARNING: Data loss!)
docker-compose down --volumes --rmi all
docker system prune -a
```

## Development Workflow

1. **Start environment**: `.\docker-scripts\start-dev.ps1`
2. **Make code changes**: Files are mounted as volumes for hot reload
3. **View logs**: `.\docker-scripts\logs.ps1`
4. **Test changes**: Access services at their respective ports
5. **Stop environment**: `.\docker-scripts\stop.ps1`

## Production Deployment

1. **Configure environment**: Update `.env` with production values
2. **Build images**: `docker-compose -f docker-compose.prod.yml build`
3. **Start services**: `docker-compose -f docker-compose.prod.yml up -d`
4. **Monitor**: Use `docker-compose logs` and health checks

## Security Considerations

- Change default passwords in production
- Use environment variables for sensitive data
- Enable SSL/TLS in production
- Regularly update base images
- Implement proper backup strategies

## Backup and Recovery

```bash
# Database backup
docker-compose exec db pg_dump -U postgres mentormatch > backup.sql

# Database restore
docker-compose exec -T db psql -U postgres mentormatch < backup.sql

# Volume backup
docker run --rm -v mentormatch_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz /data
```
