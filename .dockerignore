# Git
.git
.gitignore
README.md
ROADMAP.md

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage
*.lcov
.nyc_output

# Build outputs
build
dist
out

# Python
__pycache__
*.py[cod]
*$py.class
*.so
.Python
env
venv
ENV
env.bak
venv.bak
.pytest_cache

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp
temp
*.tmp

# Backup files
*.bak
*.backup

# Documentation
docs
*.md

# Test files
test
tests
*.test.js
*.spec.js

# CI/CD
.github
.gitlab-ci.yml
.travis.yml
.circleci

# Misc
.cache
.parcel-cache
