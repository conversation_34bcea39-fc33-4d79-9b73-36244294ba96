<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MentorMatch – منصة الإرشاد الذكي</title>
    <!-- Visualization & Content Choices: 
        - Report Info: Potential Revenue Streams -> Goal: Compare proportions -> Viz: Doughnut Chart -> Interaction: Hover tooltips -> Justification: Provides a quick, visual summary of the business model's diversity. -> Library: Chart.js (Canvas).
        - Report Info: Use Cases -> Goal: Inform/Personalize -> Viz: Interactive Tabbed Content -> Interaction: Click buttons to show relevant user story -> Justification: Allows users to self-select content, making the benefits more tangible and relatable. -> Library/Method: HTML/JS.
        - Report Info: Key Features -> Goal: Organize/Inform -> Viz: Card Grid with Unicode Icons -> Interaction: Simple, clear presentation -> Justification: Breaks down complex features into easily scannable, visually-appealing components. -> Library/Method: HTML/CSS.
        - Report Info: Next Steps -> Goal: Show process/timeline -> Viz: Vertical timeline diagram -> Interaction: Static visual -> Justification: A classic and intuitive way to represent a project roadmap. -> Library/Method: HTML/CSS/Tailwind.
    -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            scroll-behavior: smooth;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 450px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
        .section-title::after {
            content: '';
            display: block;
            width: 80px;
            height: 3px;
            background-color: #5A8F7B;
            margin: 8px auto 0;
            border-radius: 2px;
        }
        .nav-link {
            position: relative;
            transition: color 0.3s;
        }
        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -4px;
            right: 0;
            background-color: #D5A021;
            transition: width 0.3s ease-out;
        }
        .nav-link:hover::after, .nav-link.active::after {
            width: 100%;
            left: 0;
            right: auto;
        }
        .use-case-btn.active {
            background-color: #5A8F7B;
            color: #FFFFFF;
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-[#FDF8F0] text-[#4A4A4A]">

    <header id="header" class="bg-[#FDF8F0]/80 backdrop-blur-sm sticky top-0 z-50 transition-shadow duration-300">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <a href="#" class="text-2xl font-extrabold text-[#5A8F7B]">MentorMatch</a>
            <div class="hidden md:flex items-center space-x-8 lg:space-x-12">
                <a href="#solution" class="nav-link text-lg font-semibold">الحل</a>
                <a href="#features" class="nav-link text-lg font-semibold">المزايا</a>
                <a href="#use-cases" class="nav-link text-lg font-semibold">لمن التطبيق؟</a>
                <a href="#business" class="nav-link text-lg font-semibold">النموذج الربحي</a>
            </div>
            <a href="#roadmap" class="hidden md:inline-block bg-[#D5A021] text-white font-bold py-2 px-6 rounded-full hover:bg-opacity-90 transition-transform hover:scale-105">
                ابدأ رحلتك
            </a>
        </nav>
    </header>

    <main>
        <section id="hero" class="min-h-[70vh] flex items-center bg-cover bg-center" style="background-color: #F8F4EC;">
            <div class="container mx-auto px-6 text-center">
                <h1 class="text-4xl md:text-6xl font-extrabold text-[#4A4A4A] mb-4 leading-tight">MentorMatch</h1>
                <p class="text-xl md:text-2xl font-semibold text-[#5A8F7B] mb-8 max-w-3xl mx-auto">
                    منصة الإرشاد الذكي التي تربطك بالخبرة التي تحتاجها لتحقيق أهدافك.
                </p>
                <a href="#solution" class="bg-[#5A8F7B] text-white font-bold py-3 px-8 rounded-full text-lg hover:bg-opacity-90 transition-transform hover:scale-105 shadow-lg">
                    اكتشف كيف
                </a>
            </div>
        </section>

        <section id="solution" class="py-20 bg-[#FDF8F0]">
            <div class="container mx-auto px-6">
                <h2 class="section-title text-3xl font-bold text-center mb-4">الحل في MentorMatch</h2>
                <p class="text-center text-lg max-w-3xl mx-auto mb-12">
                    في عالم متسارع، أصبح الحصول على توجيه مهني وشخصي فعال تحديًا. MentorMatch يأتي كحل مبتكر لسد هذه الفجوة، حيث نستخدم قوة الذكاء الاصطناعي لإنشاء روابط إرشادية هادفة ومثمرة بين الباحثين عن المعرفة وأصحاب الخبرة، مما يجعل التطور الشخصي والمهني في متناول الجميع.
                </p>
                
                <div class="grid md:grid-cols-4 gap-8 text-center">
                    <div class="flex flex-col items-center">
                        <div class="bg-white rounded-full h-24 w-24 flex items-center justify-center shadow-md mb-4 border-4 border-[#D5A021]/50">
                            <span class="text-4xl">🔎</span>
                        </div>
                        <h3 class="font-bold text-xl mb-2">ابحث وتطابق</h3>
                        <p>أنشئ ملفك الشخصي وحدد أهدافك. سيقوم الذكاء الاصطناعي باقتراح أفضل المرشدين لك.</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="bg-white rounded-full h-24 w-24 flex items-center justify-center shadow-md mb-4 border-4 border-[#D5A021]/50">
                             <span class="text-4xl">🗓️</span>
                        </div>
                        <h3 class="font-bold text-xl mb-2">نظّم جلساتك</h3>
                        <p>تكامل سهل مع تقويمك لجدولة الجلسات بمرونة تامة تناسبك أنت ومرشدك.</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="bg-white rounded-full h-24 w-24 flex items-center justify-center shadow-md mb-4 border-4 border-[#D5A021]/50">
                             <span class="text-4xl">💬</span>
                        </div>
                        <h3 class="font-bold text-xl mb-2">تواصل وتعلّم</h3>
                        <p>استخدم أدوات الفيديو والصوت داخل التطبيق لجلسات إرشادية تفاعلية وآمنة.</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="bg-white rounded-full h-24 w-24 flex items-center justify-center shadow-md mb-4 border-4 border-[#D5A021]/50">
                             <span class="text-4xl">🚀</span>
                        </div>
                        <h3 class="font-bold text-xl mb-2">تطوّر وحقق</h3>
                        <p>تابع تقدمك نحو أهدافك من خلال تقارير مخصصة وتغذية راجعة مستمرة.</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="features" class="py-20 bg-white">
            <div class="container mx-auto px-6">
                <h2 class="section-title text-3xl font-bold text-center mb-12">مزايا تغير قواعد اللعبة</h2>
                 <p class="text-center text-lg max-w-3xl mx-auto mb-12">
                    لقد صممنا MentorMatch بمجموعة من الميزات الذكية التي تهدف إلى جعل رحلة الإرشاد سلسة، فعالة، ومخصصة بالكامل لاحتياجاتك. من المطابقة الذكية إلى تتبع الأهداف، كل أداة مصممة لتمكينك من تحقيق أقصى استفادة من تجربتك.
                </p>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="bg-[#FDF8F0] p-6 rounded-lg shadow-sm hover:shadow-lg transition-shadow">
                        <h3 class="font-bold text-xl mb-2 flex items-center"><span class="text-2xl ml-3">🧠</span> اقتراحات AI للتطابق</h3>
                        <p>خوارزمياتنا الذكية تحلل ملفك الشخصي لتقترح عليك المرشد الأكثر توافقًا مع أهدافك وتطلعاتك.</p>
                    </div>
                    <div class="bg-[#FDF8F0] p-6 rounded-lg shadow-sm hover:shadow-lg transition-shadow">
                        <h3 class="font-bold text-xl mb-2 flex items-center"><span class="text-2xl ml-3">👤</span> ملف شخصي ذكي</h3>
                        <p>ملف شامل يعرض مهاراتك، أهدافك، وحتى أسلوب التواصل المفضل لديك لتجربة مخصصة.</p>
                    </div>
                    <div class="bg-[#FDF8F0] p-6 rounded-lg shadow-sm hover:shadow-lg transition-shadow">
                        <h3 class="font-bold text-xl mb-2 flex items-center"><span class="text-2xl ml-3">📅</span> جدولة سهلة للجلسات</h3>
                        <p>نظام متكامل مع تقويمك لتنظيم المواعيد تلقائيًا بدون أي عناء.</p>
                    </div>
                    <div class="bg-[#FDF8F0] p-6 rounded-lg shadow-sm hover:shadow-lg transition-shadow">
                        <h3 class="font-bold text-xl mb-2 flex items-center"><span class="text-2xl ml-3">🎙️</span> تسجيل ملاحظات AI</h3>
                        <p>مكالمات فيديو وصوت داخل التطبيق مع ميزة تسجيل وتلخيص الملاحظات الهامة تلقائيًا.</p>
                    </div>
                    <div class="bg-[#FDF8F0] p-6 rounded-lg shadow-sm hover:shadow-lg transition-shadow">
                        <h3 class="font-bold text-xl mb-2 flex items-center"><span class="text-2xl ml-3">📈</span> متابعة مستوى التقدم</h3>
                        <p>أدوات مرئية تساعدك على تتبع تطوراتك وتحقيق أهدافك خطوة بخطوة.</p>
                    </div>
                    <div class="bg-[#FDF8F0] p-6 rounded-lg shadow-sm hover:shadow-lg transition-shadow">
                        <h3 class="font-bold text-xl mb-2 flex items-center"><span class="text-2xl ml-3">⭐</span> نظام تقييم متبادل</h3>
                        <p>تغذية راجعة بناءة من الطرفين لضمان جودة التجربة وتحسينها المستمر.</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="use-cases" class="py-20 bg-[#FDF8F0]">
            <div class="container mx-auto px-6">
                <h2 class="section-title text-3xl font-bold text-center mb-12">مصمم خصيصًا لك</h2>
                 <p class="text-center text-lg max-w-3xl mx-auto mb-12">
                    سواء كنت طالبًا على أعتاب الحياة المهنية، أو موظفًا يسعى للنمو، أو رائد أعمال يطمح للنجاح، فإن MentorMatch يوفر لك الدعم المناسب. اكتشف كيف يمكن لتطبيقنا أن يساعدك في تحقيق طموحاتك من خلال استعراض حالات الاستخدام المختلفة.
                </p>
                <div class="flex justify-center flex-wrap gap-4 mb-8">
                    <button data-case="student" class="use-case-btn active font-bold py-2 px-6 rounded-full transition-all duration-300">طالب جامعي</button>
                    <button data-case="employee" class="use-case-btn font-bold py-2 px-6 rounded-full transition-all duration-300">موظف</button>
                    <button data-case="entrepreneur" class="use-case-btn font-bold py-2 px-6 rounded-full transition-all duration-300">رائد أعمال</button>
                    <button data-case="personal" class="use-case-btn font-bold py-2 px-6 rounded-full transition-all duration-300">تطوير شخصي</button>
                </div>
                <div id="use-case-content" class="bg-white p-8 rounded-lg shadow-md max-w-3xl mx-auto min-h-[180px]">
                </div>
            </div>
        </section>

        <section id="business" class="py-20 bg-white">
            <div class="container mx-auto px-6">
                <h2 class="section-title text-3xl font-bold text-center mb-12">فرص النمو والنموذج الربحي</h2>
                 <p class="text-center text-lg max-w-3xl mx-auto mb-12">
                    يعتمد نجاحنا على نموذج عمل مستدام وقابل للتوسع يلبي احتياجات السوق المتزايدة. نقدم خططًا مرنة للمستخدمين الأفراد، بالإضافة إلى فرص شراكة استراتيجية مع المؤسسات، مما يفتح آفاقًا واسعة للنمو والإيرادات.
                </p>
                <div class="grid md:grid-cols-2 gap-12 items-center">
                    <div>
                        <h3 class="font-bold text-2xl mb-4">مصادر إيرادات متنوعة</h3>
                        <p class="mb-6">نعتمد على نموذج Freemium يتيح الوصول الأساسي مجانًا، مع خطط اشتراك مدفوعة توفر ميزات متقدمة. هذا بالإضافة إلى الشراكات مع الشركات والجامعات وعمولات على الدورات المتخصصة.</p>
                        <ul class="space-y-3">
                            <li class="flex items-center"><span class="text-[#5A8F7B] text-xl ml-2">✔</span> اشتراكات مدفوعة للمستخدمين</li>
                            <li class="flex items-center"><span class="text-[#5A8F7B] text-xl ml-2">✔</span> شراكات B2B مع المؤسسات</li>
                            <li class="flex items-center"><span class="text-[#5A8F7B] text-xl ml-2">✔</span> دورات وورش عمل حصرية</li>
                            <li class="flex items-center"><span class="text-[#5A8F7B] text-xl ml-2">✔</span> عمولات على جلسات الخبراء</li>
                        </ul>
                    </div>
                    <div class="chart-container">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <section id="roadmap" class="py-20 bg-[#FDF8F0]">
            <div class="container mx-auto px-6">
                <h2 class="section-title text-3xl font-bold text-center mb-12">خارطة الطريق نحو المستقبل</h2>
                <p class="text-center text-lg max-w-3xl mx-auto mb-12">
                    لدينا رؤية واضحة وخطوات عملية لتحويل MentorMatch من فكرة إلى واقع مؤثر. خارطة الطريق التالية توضح مراحلنا الرئيسية، بدءًا من التحقق من السوق وصولًا إلى التوسع العالمي، مما يضمن بناء منتج قوي ومستدام.
                </p>
                <div class="relative max-w-2xl mx-auto">
                    <div class="absolute left-1/2 -translate-x-1/2 h-full w-1 bg-[#5A8F7B]/30 rounded-full"></div>
                    <div class="relative mb-8">
                        <div class="absolute left-1/2 -translate-x-1/2 mt-2 w-4 h-4 bg-[#D5A021] rounded-full border-4 border-white"></div>
                        <div class="ml-auto w-[calc(50%-2rem)] bg-white p-4 rounded-lg shadow-md">
                            <h3 class="font-bold">بحث السوق والتحقق</h3>
                            <p>تحليل المنافسين وإجراء مقابلات للتحقق من حاجة السوق.</p>
                        </div>
                    </div>
                    <div class="relative mb-8 flex justify-end">
                        <div class="absolute left-1/2 -translate-x-1/2 mt-2 w-4 h-4 bg-[#D5A021] rounded-full border-4 border-white"></div>
                        <div class="mr-auto w-[calc(50%-2rem)] bg-white p-4 rounded-lg shadow-md text-right">
                            <h3 class="font-bold">تطوير النموذج الأولي</h3>
                            <p>بناء نسخة أولية (Prototype) لاختبار الميزات الأساسية وجمع الملاحظات.</p>
                        </div>
                    </div>
                    <div class="relative mb-8">
                        <div class="absolute left-1/2 -translate-x-1/2 mt-2 w-4 h-4 bg-[#D5A021] rounded-full border-4 border-white"></div>
                        <div class="ml-auto w-[calc(50%-2rem)] bg-white p-4 rounded-lg shadow-md">
                             <h3 class="font-bold">الإطلاق الأولي والتسويق</h3>
                             <p>إطلاق التطبيق لمجموعة محدودة وبناء استراتيجية تسويق فعالة.</p>
                        </div>
                    </div>
                     <div class="relative">
                        <div class="absolute left-1/2 -translate-x-1/2 mt-2 w-4 h-4 bg-[#D5A021] rounded-full border-4 border-white"></div>
                        <div class="mr-auto w-[calc(50%-2rem)] bg-white p-4 rounded-lg shadow-md text-right">
                             <h3 class="font-bold">التوسع والنمو</h3>
                             <p>إضافة مجالات جديدة، تطوير الميزات، والتوسع في أسواق عالمية.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="bg-[#4A4A4A] text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p>&copy; 2025 MentorMatch. جميع الحقوق محفوظة.</p>
            <p>تم تصميم هذا العرض التفاعلي لعرض فكرة التطبيق.</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const revenueCtx = document.getElementById('revenueChart');
            if(revenueCtx) {
                new Chart(revenueCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['اشتراكات المستخدمين', 'شراكات B2B', 'دورات وورش عمل', 'عمولات'],
                        datasets: [{
                            label: 'مصادر الإيرادات',
                            data: [50, 30, 15, 5],
                            backgroundColor: [
                                '#5A8F7B',
                                '#84A98C',
                                '#D5A021',
                                '#F0C987'
                            ],
                            borderColor: '#FDF8F0',
                            borderWidth: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    color: '#4A4A4A',
                                    font: {
                                        family: 'Tajawal',
                                        size: 14,
                                        weight: 'bold'
                                    }
                                }
                            }
                        }
                    }
                });
            }

            const useCases = {
                student: {
                    title: 'طالب جامعي يبحث عن انطلاقة قوية',
                    content: 'هل أنت طالب على وشك التخرج؟ MentorMatch يربطك بخبراء في مجالك ليقدموا لك نصائح قيمة حول بناء سيرتك الذاتية، التحضير لمقابلات العمل، وفهم متطلبات سوق العمل الحقيقي.'
                },
                employee: {
                    title: 'موظف يطمح للنمو أو التغيير',
                    content: 'سواء كنت تسعى لترقية في منصبك الحالي أو تفكر في الانتقال إلى مجال جديد بالكامل، يمكنك العثور على مرشد مر بنفس التجربة ليشاركك خريطة الطريق نحو النجاح.'
                },
                entrepreneur: {
                    title: 'رائد أعمال يبحث عن مستشار خبير',
                    content: 'من فكرة إلى شركة ناجحة، الطريق مليء بالتحديات. احصل على استشارات متخصصة في التسويق، التمويل، أو تطوير المنتجات من رواد أعمال وخبراء استثمار.'
                },
                personal: {
                    title: 'شخص يسعى لتطوير مهاراته',
                    content: 'التطور لا يتوقف عند الجانب المهني. اعمل مع مرشدين متخصصين في تطوير الذكاء العاطفي، مهارات القيادة، إدارة الوقت، أو أي مهارة شخصية تسعى لاكتسابها.'
                }
            };

            const useCaseButtons = document.querySelectorAll('.use-case-btn');
            const useCaseContentDiv = document.getElementById('use-case-content');
            
            function updateUseCaseContent(caseKey) {
                 if(!useCases[caseKey] || !useCaseContentDiv) return;
                 const data = useCases[caseKey];
                 useCaseContentDiv.innerHTML = `
                    <h3 class="font-bold text-2xl mb-3 text-[#5A8F7B]">${data.title}</h3>
                    <p class="text-lg">${data.content}</p>
                 `;
            }
            
            useCaseButtons.forEach(button => {
                button.addEventListener('click', () => {
                    useCaseButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');
                    const caseKey = button.getAttribute('data-case');
                    updateUseCaseContent(caseKey);
                });
            });

            updateUseCaseContent('student');

            const header = document.getElementById('header');
            window.addEventListener('scroll', () => {
                if (window.scrollY > 50) {
                    header.classList.add('shadow-md');
                } else {
                    header.classList.remove('shadow-md');
                }
            });

            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('main section');

            window.addEventListener('scroll', () => {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    if (pageYOffset >= sectionTop - 150) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href').includes(current)) {
                        link.classList.add('active');
                    }
                });
            });
        });
    </script>
</body>
</html>
