# Clean up Docker resources
Write-Host "Cleaning up MentorMatch Docker resources..." -ForegroundColor Yellow

# Stop all services
Write-Host "Stopping all services..." -ForegroundColor Blue
docker-compose down
docker-compose -f docker-compose.prod.yml down

# Remove containers
Write-Host "Removing containers..." -ForegroundColor Blue
docker-compose down --remove-orphans

# Remove images (optional - uncomment if needed)
# Write-Host "Removing images..." -ForegroundColor Blue
# docker-compose down --rmi all

# Remove volumes (WARNING: This will delete all data!)
$removeVolumes = Read-Host "Do you want to remove volumes? This will delete all data! (y/N)"
if ($removeVolumes -eq "y" -or $removeVolumes -eq "Y") {
    Write-Host "Removing volumes..." -ForegroundColor Red
    docker-compose down --volumes
    docker volume prune -f
}

# Clean up unused Docker resources
Write-Host "Cleaning up unused Docker resources..." -ForegroundColor Blue
docker system prune -f

Write-Host "Cleanup completed!" -ForegroundColor Green
